package services

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// ConfigService 配置服务 (迁移自原main.go的配置加载功能)
type ConfigService struct{}

// NewConfigService 创建配置服务实例
func NewConfigService() *ConfigService {
	return &ConfigService{}
}

// ServerConfigFull 完整的服务器配置结构 (迁移自原main.go ServerConfig)
type ServerConfigFull struct {
	Listen        string `json:"listen"`
	Port          string `json:"port"`
	LoginUsername string `json:"login_username"`
	LoginPassword string `json:"login_password"`
	Servers       []struct {
		ID      int    `json:"mid"`
		Name    string `json:"name"`
		Host    string `json:"host"`
		Enabled bool   `json:"enabled"`
	} `json:"servers"`
	Database struct {
		Type string `json:"type"`
		Path string `json:"path"`
	} `json:"database"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	SecureCookie bool   `json:"secure_cookie"`
	Password     string `json:"password"`
}

// ClientConfigFull 完整的客户端配置结构 (迁移自原main.go ClientConfig)
type ClientConfigFull struct {
	MID       int    `json:"mid"`
	Server    string `json:"server"`
	Port      string `json:"port"`
	WebSocket struct {
		Enabled bool   `json:"enabled"`
		Path    string `json:"path"`
	} `json:"websocket"`
	Database struct {
		Type      string `json:"type"`
		Path      string `json:"path"`
		Retention string `json:"retention"`
	} `json:"database"`
	Password   string `json:"password"`
	ServerInfo struct {
		Name         string `json:"name"`
		Tag          string `json:"tag"`
		IPv4         string `json:"ipv4"`
		IPv6         string `json:"ipv6"`
		AutoDetectIP bool   `json:"auto_detect_ip"`
	} `json:"server_info"`
}

// LoadServerConfig 加载服务器配置 (迁移自原main.go loadServerConfig)
func (cs *ConfigService) LoadServerConfig(filePath string) (*ServerConfigFull, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取服务器配置文件 %s 失败: %v", filePath, err)
	}

	var config ServerConfigFull
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("解析服务器配置文件 %s 失败: %v", filePath, err)
	}

	// 验证配置
	if err := cs.validateServerConfig(&config); err != nil {
		return nil, fmt.Errorf("服务器配置验证失败: %v", err)
	}

	return &config, nil
}

// LoadClientConfig 加载客户端配置 (迁移自原main.go loadClientConfig)
func (cs *ConfigService) LoadClientConfig(filePath string) (*ClientConfigFull, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取客户端配置文件 %s 失败: %v", filePath, err)
	}

	var config ClientConfigFull
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("解析客户端配置文件 %s 失败: %v", filePath, err)
	}

	// 验证配置
	if err := cs.validateClientConfig(&config); err != nil {
		return nil, fmt.Errorf("客户端配置验证失败: %v", err)
	}

	return &config, nil
}

// SaveServerConfig 保存服务器配置
func (cs *ConfigService) SaveServerConfig(config *ServerConfigFull, filePath string) error {
	// 验证配置
	if err := cs.validateServerConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 创建备份
	if err := cs.createBackup(filePath); err != nil {
		return fmt.Errorf("创建配置备份失败: %v", err)
	}

	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}

// SaveClientConfig 保存客户端配置
func (cs *ConfigService) SaveClientConfig(config *ClientConfigFull, filePath string) error {
	// 验证配置
	if err := cs.validateClientConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 创建备份
	if err := cs.createBackup(filePath); err != nil {
		return fmt.Errorf("创建配置备份失败: %v", err)
	}

	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}

// validateServerConfig 验证服务器配置
func (cs *ConfigService) validateServerConfig(config *ServerConfigFull) error {
	if config.Listen == "" {
		return fmt.Errorf("监听地址不能为空")
	}

	if config.Port == "" {
		return fmt.Errorf("端口不能为空")
	}

	if config.LoginUsername == "" {
		return fmt.Errorf("登录用户名不能为空")
	}

	if config.LoginPassword == "" {
		return fmt.Errorf("登录密码不能为空")
	}

	if config.Password == "" {
		return fmt.Errorf("WebSocket密码不能为空")
	}

	if config.Database.Type == "" {
		config.Database.Type = "sqlite" // 默认值
	}

	if config.Database.Path == "" {
		config.Database.Path = "./monitor.db" // 默认值
	}

	// 验证服务器列表
	if len(config.Servers) == 0 {
		return fmt.Errorf("至少需要配置一个服务器")
	}

	for i, server := range config.Servers {
		if server.ID <= 0 {
			return fmt.Errorf("服务器 %d 的ID必须大于0", i)
		}
		if server.Name == "" {
			return fmt.Errorf("服务器 %d 的名称不能为空", i)
		}
	}

	return nil
}

// validateClientConfig 验证客户端配置
func (cs *ConfigService) validateClientConfig(config *ClientConfigFull) error {
	if config.MID <= 0 {
		return fmt.Errorf("客户端ID (MID) 必须大于0")
	}

	if config.Server == "" {
		return fmt.Errorf("服务器地址不能为空")
	}

	if config.Port == "" {
		return fmt.Errorf("服务器端口不能为空")
	}

	if config.Password == "" {
		return fmt.Errorf("连接密码不能为空")
	}

	if config.ServerInfo.Name == "" {
		return fmt.Errorf("服务器名称不能为空")
	}

	if config.Database.Type == "" {
		config.Database.Type = "sqlite" // 默认值
	}

	if config.Database.Path == "" {
		config.Database.Path = "./client.db" // 默认值
	}

	return nil
}

// createBackup 创建配置文件备份
func (cs *ConfigService) createBackup(filePath string) error {
	// 检查原文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil // 原文件不存在，无需备份
	}

	// 创建备份文件名
	backupPath := filePath + ".backup"

	// 读取原文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	// 写入备份文件
	return os.WriteFile(backupPath, data, 0644)
}

// GetExecutableDir 获取可执行文件的目录 (迁移自原main.go getExecutableDir)
func (cs *ConfigService) GetExecutableDir() string {
	execPath, err := os.Executable()
	if err != nil {
		return "." // 返回当前目录作为后备
	}
	return filepath.Dir(execPath)
}

// GetFilePath 获取相对于可执行文件的文件路径 (迁移自原main.go getFilePath)
func (cs *ConfigService) GetFilePath(filename string) string {
	execDir := cs.GetExecutableDir()
	return filepath.Join(execDir, filename)
}

// GetDefaultServerConfigPath 获取默认服务器配置文件路径
func (cs *ConfigService) GetDefaultServerConfigPath() string {
	return cs.GetFilePath("../server.json")
}

// GetDefaultClientConfigPath 获取默认客户端配置文件路径
func (cs *ConfigService) GetDefaultClientConfigPath() string {
	return cs.GetFilePath("../client.json")
}

// CreateDefaultServerConfig 创建默认服务器配置
func (cs *ConfigService) CreateDefaultServerConfig() *ServerConfigFull {
	return &ServerConfigFull{
		Listen:        "0.0.0.0",
		Port:          "7788",
		LoginUsername: "xctcc",
		LoginPassword: "960423Wc@",
		Servers: []struct {
			ID      int    `json:"mid"`
			Name    string `json:"name"`
			Host    string `json:"host"`
			Enabled bool   `json:"enabled"`
		}{
			{ID: 1, Name: "服务器1", Host: "localhost", Enabled: true},
		},
		Database: struct {
			Type string `json:"type"`
			Path string `json:"path"`
		}{
			Type: "sqlite",
			Path: "./monitor.db",
		},
		WebSocket: struct {
			Enabled bool   `json:"enabled"`
			Path    string `json:"path"`
		}{
			Enabled: true,
			Path:    "/ws",
		},
		SecureCookie: false,
		Password:     "your-websocket-password",
	}
}

// CreateDefaultClientConfig 创建默认客户端配置
func (cs *ConfigService) CreateDefaultClientConfig() *ClientConfigFull {
	return &ClientConfigFull{
		MID:    1,
		Server: "localhost",
		Port:   "7788",
		WebSocket: struct {
			Enabled bool   `json:"enabled"`
			Path    string `json:"path"`
		}{
			Enabled: true,
			Path:    "/ws",
		},
		Database: struct {
			Type      string `json:"type"`
			Path      string `json:"path"`
			Retention string `json:"retention"`
		}{
			Type:      "sqlite",
			Path:      "./client.db",
			Retention: "30d",
		},
		Password: "your-websocket-password",
		ServerInfo: struct {
			Name         string `json:"name"`
			Tag          string `json:"tag"`
			IPv4         string `json:"ipv4"`
			IPv6         string `json:"ipv6"`
			AutoDetectIP bool   `json:"auto_detect_ip"`
		}{
			Name:         "我的服务器",
			Tag:          "production",
			IPv4:         "",
			IPv6:         "",
			AutoDetectIP: true,
		},
	}
}

// ConfigExists 检查配置文件是否存在
func (cs *ConfigService) ConfigExists(filePath string) bool {
	_, err := os.Stat(filePath)
	return !os.IsNotExist(err)
}
