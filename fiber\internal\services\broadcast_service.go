package services

import (
	"log"
	"sync"
	"time"

	"server-monitor-fiber/internal/database"
	"server-monitor-fiber/internal/websocket"
)

// BroadcastService 实时数据广播服务 (迁移自原main.go 2000-2631行的广播功能)
type BroadcastService struct {
	serverStatusRepo database.ServerStatusRepository
	serverService    *ServerService
	ticker           *time.Ticker
	stopChan         chan bool
	isRunning        bool
	mutex            sync.RWMutex
	serverConfig     *ServerConfig
}

// ServerConfig 服务器配置 (简化版)
type ServerConfig struct {
	Servers []struct {
		ID      int    `json:"mid"`
		Name    string `json:"name"`
		Host    string `json:"host"`
		Enabled bool   `json:"enabled"`
	} `json:"servers"`
}

// NewBroadcastService 创建广播服务实例
func NewBroadcastService(
	statusRepo database.ServerStatusRepository,
	serverService *ServerService,
	config *ServerConfig,
) *BroadcastService {
	return &BroadcastService{
		serverStatusRepo: statusRepo,
		serverService:    serverService,
		stopChan:         make(chan bool),
		serverConfig:     config,
	}
}

// Start 启动实时数据广播 (迁移自原main.go startRealTimeDataBroadcast)
func (bs *BroadcastService) Start() {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()

	if bs.isRunning {
		log.Println("广播服务已经在运行")
		return
	}

	bs.ticker = time.NewTicker(1 * time.Second) // 每1秒广播一次
	bs.isRunning = true

	log.Println("启动实时数据广播服务 (1秒间隔)")

	go bs.broadcastLoop()
}

// Stop 停止实时数据广播
func (bs *BroadcastService) Stop() {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()

	if !bs.isRunning {
		log.Println("广播服务未运行")
		return
	}

	bs.ticker.Stop()
	bs.stopChan <- true
	bs.isRunning = false

	log.Println("实时数据广播服务已停止")
}

// IsRunning 检查广播服务是否正在运行
func (bs *BroadcastService) IsRunning() bool {
	bs.mutex.RLock()
	defer bs.mutex.RUnlock()
	return bs.isRunning
}

// broadcastLoop 广播循环
func (bs *BroadcastService) broadcastLoop() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("广播循环发生panic: %v", r)
		}
	}()

	for {
		select {
		case <-bs.ticker.C:
			bs.broadcastSystemStats()
		case <-bs.stopChan:
			log.Println("收到停止信号，退出广播循环")
			return
		}
	}
}

// broadcastSystemStats 广播系统统计信息 (迁移自原main.go broadcastSystemStats)
func (bs *BroadcastService) broadcastSystemStats() {
	// 收集所有服务器ID
	var serverIDs []int
	for _, s := range bs.serverConfig.Servers {
		if s.Enabled {
			serverIDs = append(serverIDs, s.ID)
		}
	}

	if len(serverIDs) == 0 {
		return // 没有启用的服务器
	}

	// 批量获取所有服务器状态
	allServerStatuses, err := bs.serverStatusRepo.GetAllServerStatuses(serverIDs)
	if err != nil {
		log.Printf("获取服务器状态失败: %v", err)
		// 继续使用可用数据或发送空统计信息
	}

	// 创建快速查找映射
	serverStatusMap := make(map[int]database.ServerStatus)
	for _, ss := range allServerStatuses {
		serverStatusMap[ss.ID] = ss
	}

	// 为每个配置的服务器广播数据
	for _, s := range bs.serverConfig.Servers {
		if !s.Enabled {
			continue
		}

		// 默认空统计信息，避免前端除零错误
		stats := &database.StatusInfo{
			MemTotal: 1,
		}

		// 如果有实际数据，使用实际数据
		if ss, ok := serverStatusMap[s.ID]; ok {
			stats = &database.StatusInfo{
				CPU:         ss.StatusCPU,
				MemUsed:     ss.StatusMemUsed,
				MemTotal:    ss.StatusMemTotal,
				DiskUsed:    ss.StatusDiskUsed,
				DiskTotal:   ss.StatusDiskTotal,
				NetInSpeed:  ss.StatusNetInSpeed,
				NetOutSpeed: ss.StatusNetOutSpeed,
				Uptime:      ss.StatusUptime,
				Load1:       ss.StatusLoad1,
				Load5:       ss.StatusLoad5,
				Load15:      ss.StatusLoad15,
			}
		} else {
			log.Printf("服务器ID %d (%s) 没有实时统计数据，广播默认/空统计信息", s.ID, s.Name)
		}

		// 创建广播消息
		message := websocket.WSMessage{
			Type:     websocket.WSMsgTypeSystemStatsBroadcast,
			ServerID: s.ID,
			Data: websocket.SystemStatsBroadcastMessage{
				ServerID: s.ID,
				Data: websocket.SystemStats{
					CPU:            stats.CPU,
					MemUsed:        stats.MemUsed,
					MemTotal:       stats.MemTotal,
					Memory:         bs.calculatePercentage(stats.MemUsed, stats.MemTotal),
					DiskUsed:       stats.DiskUsed,
					DiskTotal:      stats.DiskTotal,
					Disk:           bs.calculatePercentage(stats.DiskUsed, stats.DiskTotal),
					NetInSpeed:     stats.NetInSpeed,
					NetOutSpeed:    stats.NetOutSpeed,
					NetInTransfer:  0, // 可以添加网络传输总量
					NetOutTransfer: 0,
					Uptime:         stats.Uptime,
					LoadAvg:        []float64{stats.Load1, stats.Load5, stats.Load15},
					Processes:      0, // 可以添加进程数量
				},
			},
			Timestamp: time.Now(),
		}

		// 广播到订阅的前端连接
		websocket.GlobalManager.BroadcastToFrontends(s.ID, message)
	}
}

// calculatePercentage 计算百分比
func (bs *BroadcastService) calculatePercentage(used, total uint64) float64 {
	if total == 0 {
		return 0
	}
	return (float64(used) / float64(total)) * 100
}

// GetLatestClientStats 获取最新的客户端统计信息 (迁移自原main.go getLatestClientStats)
func (bs *BroadcastService) GetLatestClientStats(serverID int) (*database.StatusInfo, bool) {
	// 从数据库获取最新统计信息
	serverStatus, err := bs.serverStatusRepo.GetServerStatus(serverID)
	if err != nil {
		// 返回默认StatusInfo和false表示未找到
		return &database.StatusInfo{
			MemTotal: 1, // 避免前端除零错误
		}, false
	}

	// 转换为StatusInfo
	statusInfo := serverStatus.ToStatusInfo()
	return statusInfo, true
}

// BroadcastServiceUpdate 广播服务更新 (用于服务控制后的更新)
func (bs *BroadcastService) BroadcastServiceUpdate(serverID int, serviceType string, services interface{}) {
	message := websocket.WSMessage{
		Type:     "service_list_update",
		ServerID: serverID,
		Data: map[string]interface{}{
			"services":    services,
			"serviceType": serviceType,
		},
		Timestamp: time.Now(),
	}

	websocket.GlobalManager.BroadcastToFrontends(serverID, message)
	log.Printf("广播服务器 %d 的 %s 服务列表更新", serverID, serviceType)
}

// BroadcastServerStatusUpdate 广播服务器状态更新
func (bs *BroadcastService) BroadcastServerStatusUpdate(serverID int, statusInfo *database.StatusInfo) {
	message := websocket.WSMessage{
		Type:     websocket.WSMsgTypeSystemStatsBroadcast,
		ServerID: serverID,
		Data: websocket.SystemStatsBroadcastMessage{
			ServerID: serverID,
			Data: websocket.SystemStats{
				CPU:            statusInfo.CPU,
				MemUsed:        statusInfo.MemUsed,
				MemTotal:       statusInfo.MemTotal,
				Memory:         bs.calculatePercentage(statusInfo.MemUsed, statusInfo.MemTotal),
				DiskUsed:       statusInfo.DiskUsed,
				DiskTotal:      statusInfo.DiskTotal,
				Disk:           bs.calculatePercentage(statusInfo.DiskUsed, statusInfo.DiskTotal),
				NetInSpeed:     statusInfo.NetInSpeed,
				NetOutSpeed:    statusInfo.NetOutSpeed,
				NetInTransfer:  0,
				NetOutTransfer: 0,
				Uptime:         statusInfo.Uptime,
				LoadAvg:        []float64{statusInfo.Load1, statusInfo.Load5, statusInfo.Load15},
				Processes:      0,
			},
		},
		Timestamp: time.Now(),
	}

	websocket.GlobalManager.BroadcastToFrontends(serverID, message)
}

// GetBroadcastStats 获取广播统计信息
func (bs *BroadcastService) GetBroadcastStats() map[string]interface{} {
	bs.mutex.RLock()
	defer bs.mutex.RUnlock()

	stats := map[string]interface{}{
		"is_running":     bs.isRunning,
		"enabled_servers": len(bs.getEnabledServers()),
		"broadcast_interval": "1s",
	}

	if bs.isRunning {
		stats["uptime"] = time.Since(time.Now()).String() // 这里应该记录实际的启动时间
	}

	return stats
}

// getEnabledServers 获取启用的服务器列表
func (bs *BroadcastService) getEnabledServers() []int {
	var serverIDs []int
	for _, s := range bs.serverConfig.Servers {
		if s.Enabled {
			serverIDs = append(serverIDs, s.ID)
		}
	}
	return serverIDs
}

// UpdateServerConfig 更新服务器配置
func (bs *BroadcastService) UpdateServerConfig(config *ServerConfig) {
	bs.mutex.Lock()
	defer bs.mutex.Unlock()
	
	bs.serverConfig = config
	log.Println("广播服务的服务器配置已更新")
}

// ForceUpdate 强制更新广播
func (bs *BroadcastService) ForceUpdate() {
	if bs.IsRunning() {
		go bs.broadcastSystemStats()
		log.Println("强制执行系统统计广播")
	}
}
