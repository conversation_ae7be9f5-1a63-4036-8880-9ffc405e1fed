package services

import (
	"fmt"
	"log"
	"os/exec"
	"runtime"
	"strings"

	"server-monitor-fiber/internal/database"
)

// ServiceControlService 服务控制服务层 (迁移自原main.go的服务控制功能)
type ServiceControlService struct {
	serviceRepo database.ServiceRepository
}

// NewServiceControlService 创建服务控制服务实例
func NewServiceControlService(serviceRepo database.ServiceRepository) *ServiceControlService {
	return &ServiceControlService{
		serviceRepo: serviceRepo,
	}
}

// ControlService 控制服务 (迁移自原main.go controlService函数)
func (s *ServiceControlService) ControlService(serviceType, serviceName, action string) error {
	log.Printf("控制服务: type=%s, name=%s, action=%s", serviceType, serviceName, action)

	switch serviceType {
	case "supervisor":
		return s.controlSupervisorService(serviceName, action)
	case "systemd":
		return s.controlSystemdService(serviceName, action)
	case "docker":
		return s.controlDockerService(serviceName, action)
	default:
		return fmt.Errorf("不支持的服务类型: %s", serviceType)
	}
}

// controlSupervisorService 控制Supervisor服务
func (s *ServiceControlService) controlSupervisorService(serviceName, action string) error {
	var cmd *exec.Cmd

	switch action {
	case "start":
		cmd = exec.Command("supervisorctl", "start", serviceName)
	case "stop":
		cmd = exec.Command("supervisorctl", "stop", serviceName)
	case "restart":
		cmd = exec.Command("supervisorctl", "restart", serviceName)
	default:
		return fmt.Errorf("不支持的Supervisor操作: %s", action)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("Supervisor命令执行失败: %v, 输出: %s", err, string(output))
	}

	log.Printf("Supervisor服务 %s %s 成功: %s", serviceName, action, string(output))
	return nil
}

// controlSystemdService 控制Systemd服务
func (s *ServiceControlService) controlSystemdService(serviceName, action string) error {
	if runtime.GOOS == "windows" {
		return fmt.Errorf("Windows系统不支持systemd服务")
	}

	var cmd *exec.Cmd

	switch action {
	case "start":
		cmd = exec.Command("systemctl", "start", serviceName)
	case "stop":
		cmd = exec.Command("systemctl", "stop", serviceName)
	case "restart":
		cmd = exec.Command("systemctl", "restart", serviceName)
	case "reload":
		cmd = exec.Command("systemctl", "reload", serviceName)
	case "enable":
		cmd = exec.Command("systemctl", "enable", serviceName)
	case "disable":
		cmd = exec.Command("systemctl", "disable", serviceName)
	default:
		return fmt.Errorf("不支持的systemd操作: %s", action)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("systemd命令执行失败: %v, 输出: %s", err, string(output))
	}

	log.Printf("systemd服务 %s %s 成功: %s", serviceName, action, string(output))
	return nil
}

// controlDockerService 控制Docker服务
func (s *ServiceControlService) controlDockerService(serviceName, action string) error {
	var cmd *exec.Cmd

	switch action {
	case "start":
		cmd = exec.Command("docker", "start", serviceName)
	case "stop":
		cmd = exec.Command("docker", "stop", serviceName)
	case "restart":
		cmd = exec.Command("docker", "restart", serviceName)
	case "pause":
		cmd = exec.Command("docker", "pause", serviceName)
	case "unpause":
		cmd = exec.Command("docker", "unpause", serviceName)
	default:
		return fmt.Errorf("不支持的Docker操作: %s", action)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("Docker命令执行失败: %v, 输出: %s", err, string(output))
	}

	log.Printf("Docker服务 %s %s 成功: %s", serviceName, action, string(output))
	return nil
}

// GetServiceLogs 获取服务日志 (迁移自原main.go getServiceLogs函数)
func (s *ServiceControlService) GetServiceLogs(serviceType, serviceName string) (string, error) {
	log.Printf("获取服务日志: type=%s, name=%s", serviceType, serviceName)

	switch serviceType {
	case "supervisor":
		return s.getSupervisorLogs(serviceName)
	case "systemd":
		return s.getSystemdLogs(serviceName)
	case "docker":
		return s.getDockerLogs(serviceName)
	default:
		return "", fmt.Errorf("不支持的服务类型: %s", serviceType)
	}
}

// getSupervisorLogs 获取Supervisor服务日志
func (s *ServiceControlService) getSupervisorLogs(serviceName string) (string, error) {
	cmd := exec.Command("supervisorctl", "tail", "-1000", serviceName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("获取Supervisor日志失败: %v, 输出: %s", err, string(output))
	}

	return string(output), nil
}

// getSystemdLogs 获取Systemd服务日志
func (s *ServiceControlService) getSystemdLogs(serviceName string) (string, error) {
	if runtime.GOOS == "windows" {
		return "", fmt.Errorf("Windows系统不支持systemd服务")
	}

	cmd := exec.Command("journalctl", "-u", serviceName, "-n", "1000", "--no-pager")
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("获取systemd日志失败: %v, 输出: %s", err, string(output))
	}

	return string(output), nil
}

// getDockerLogs 获取Docker服务日志
func (s *ServiceControlService) getDockerLogs(serviceName string) (string, error) {
	cmd := exec.Command("docker", "logs", "--tail", "1000", serviceName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("获取Docker日志失败: %v, 输出: %s", err, string(output))
	}

	return string(output), nil
}

// GetServiceStatus 获取服务状态
func (s *ServiceControlService) GetServiceStatus(serviceType, serviceName string) (string, error) {
	switch serviceType {
	case "supervisor":
		return s.getSupervisorStatus(serviceName)
	case "systemd":
		return s.getSystemdStatus(serviceName)
	case "docker":
		return s.getDockerStatus(serviceName)
	default:
		return "", fmt.Errorf("不支持的服务类型: %s", serviceType)
	}
}

// getSupervisorStatus 获取Supervisor服务状态
func (s *ServiceControlService) getSupervisorStatus(serviceName string) (string, error) {
	cmd := exec.Command("supervisorctl", "status", serviceName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "unknown", fmt.Errorf("获取Supervisor状态失败: %v", err)
	}

	outputStr := string(output)
	if strings.Contains(outputStr, "RUNNING") {
		return "running", nil
	} else if strings.Contains(outputStr, "STOPPED") {
		return "stopped", nil
	} else if strings.Contains(outputStr, "FATAL") {
		return "failed", nil
	}

	return "unknown", nil
}

// getSystemdStatus 获取Systemd服务状态
func (s *ServiceControlService) getSystemdStatus(serviceName string) (string, error) {
	if runtime.GOOS == "windows" {
		return "unknown", fmt.Errorf("Windows系统不支持systemd服务")
	}

	cmd := exec.Command("systemctl", "is-active", serviceName)
	output, err := cmd.CombinedOutput()
	
	outputStr := strings.TrimSpace(string(output))
	switch outputStr {
	case "active":
		return "running", nil
	case "inactive":
		return "stopped", nil
	case "failed":
		return "failed", nil
	default:
		return "unknown", nil
	}
}

// getDockerStatus 获取Docker服务状态
func (s *ServiceControlService) getDockerStatus(serviceName string) (string, error) {
	cmd := exec.Command("docker", "inspect", "--format={{.State.Status}}", serviceName)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "unknown", fmt.Errorf("获取Docker状态失败: %v", err)
	}

	status := strings.TrimSpace(string(output))
	switch status {
	case "running":
		return "running", nil
	case "exited":
		return "stopped", nil
	case "paused":
		return "paused", nil
	case "restarting":
		return "restarting", nil
	default:
		return status, nil
	}
}

// ListServices 列出服务 (迁移自原main.go的服务列表功能)
func (s *ServiceControlService) ListServices(serverID int, serviceType string) (interface{}, error) {
	log.Printf("列出服务: serverID=%d, type=%s", serverID, serviceType)

	switch serviceType {
	case "supervisor":
		return s.serviceRepo.GetSupervisorServices(serverID)
	case "systemd":
		return s.serviceRepo.GetSystemdServices(serverID)
	case "docker":
		return s.serviceRepo.GetDockerServices(serverID)
	default:
		return nil, fmt.Errorf("不支持的服务类型: %s", serviceType)
	}
}

// RefreshServices 刷新服务列表
func (s *ServiceControlService) RefreshServices(serverID int) error {
	log.Printf("刷新服务器 %d 的服务列表", serverID)

	// 这里可以实现刷新逻辑
	// 例如重新扫描系统中的服务并更新数据库

	return nil
}

// ValidateServiceOperation 验证服务操作
func (s *ServiceControlService) ValidateServiceOperation(serviceType, serviceName, action string) error {
	if serviceType == "" {
		return fmt.Errorf("服务类型不能为空")
	}

	if serviceName == "" {
		return fmt.Errorf("服务名称不能为空")
	}

	if action == "" {
		return fmt.Errorf("操作类型不能为空")
	}

	// 验证服务类型
	validTypes := []string{"supervisor", "systemd", "docker"}
	isValidType := false
	for _, validType := range validTypes {
		if serviceType == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("不支持的服务类型: %s", serviceType)
	}

	// 验证操作类型
	validActions := []string{"start", "stop", "restart", "reload", "enable", "disable", "pause", "unpause"}
	isValidAction := false
	for _, validAction := range validActions {
		if action == validAction {
			isValidAction = true
			break
		}
	}
	if !isValidAction {
		return fmt.Errorf("不支持的操作类型: %s", action)
	}

	return nil
}
