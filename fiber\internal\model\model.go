package model

import "time"

// ServerInfo 结构体用于存储服务器的基本信息和最新状态
type ServerInfo struct {
	ID         int        `json:"id"`
	Name       string     `json:"name"`
	IPv4       string     `json:"ipv4"`
	ValidIP    string     `json:"valid_ip"`
	LastActive int64      `json:"last_active"` // Unix timestamp in seconds
	Status     StatusInfo `json:"status"`
	Host       HostInfo   `json:"host"`
}

// StatusInfo 结构体用于存储服务器的实时状态信息
type StatusInfo struct {
	CPU            float64 `json:"cpu"`
	MemUsed        uint64  `json:"mem_used"`
	MemTotal       uint64  `json:"mem_total"`
	DiskUsed       uint64  `json:"disk_used"`
	DiskTotal      uint64  `json:"disk_total"`
	NetInTransfer  uint64  `json:"net_in_transfer"`
	NetOutTransfer uint64  `json:"net_out_transfer"`
	NetInSpeed     uint64  `json:"net_in_speed"`
	NetOutSpeed    uint64  `json:"net_out_speed"`
	Uptime         uint64  `json:"uptime"`
}

// HostInfo 结构体用于存储服务器的操作系统和硬件信息
type HostInfo struct {
	Platform        string `json:"platform"`
	PlatformVersion string `json:"platform_version"`
}

// Service 结构体用于存储服务信息
type Service struct {
	Name   string `json:"name"`
	Status string `json:"status"`
	Uptime uint64 `json:"uptime"`
}

// FrontendServerDetails 结构体用于前端展示服务器列表
type FrontendServerDetails struct {
	ID             int     `json:"id"`
	Name           string  `json:"name"`
	IP             string  `json:"ip"`
	Hostname       string  `json:"hostname"`
	OS             string  `json:"os"`
	Status         string  `json:"status"` // online, offline, warning
	CPU            float64 `json:"cpu"`
	Memory         float64 `json:"memory"` // Percentage
	NetInSpeed     uint64  `json:"NetInSpeed"`
	NetOutSpeed    uint64  `json:"NetOutSpeed"`
	NetInTransfer  uint64  `json:"NetInTransfer"`
	NetOutTransfer uint64  `json:"NetOutTransfer"`
	Uptime         string  `json:"uptime"` // Formatted string
	LastActive     int64   `json:"lastActive"`
	IsPlaceholder  bool    `json:"isPlaceholder"`
}

// FrontendWSMessage WebSocket 消息结构体，用于前端和后端通信
type FrontendWSMessage struct {
	Type      string      `json:"type"`
	ServerID  int         `json:"ServerID"`
	Timestamp time.Time   `json:"Timestamp"`
	Data      interface{} `json:"Data"` // 可以是 StatusInfo 或其他数据
}
