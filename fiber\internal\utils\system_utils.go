package utils

import (
	"encoding/json"
	"fmt"
	"log"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"

	proc "github.com/shirou/gopsutil/v4/process"
)

// SystemUtils 系统工具类 (迁移自原main.go 1500-2000行的工具函数)
type SystemUtils struct{}

// NewSystemUtils 创建系统工具实例
func NewSystemUtils() *SystemUtils {
	return &SystemUtils{}
}

// ExecuteCommand 执行系统命令并返回输出 (迁移自原main.go executeCommand)
func (su *SystemUtils) ExecuteCommand(command string, args ...string) (string, error) {
	cmd := exec.Command(command, args...)
	output, err := cmd.CombinedOutput()
	return string(output), err
}

// ParseSystemdProperty 从systemctl show输出中解析属性值 (迁移自原main.go)
func (su *SystemUtils) ParseSystemdProperty(output, property string) string {
	prefix := property + "="
	for _, line := range strings.Split(output, "\n") {
		if strings.HasPrefix(line, prefix) {
			return strings.TrimSpace(strings.TrimPrefix(line, prefix))
		}
	}
	return ""
}

// FormatUptime 格式化秒数为人类可读的运行时间 (迁移自原main.go)
func (su *SystemUtils) FormatUptime(seconds int64) string {
	if seconds < 0 {
		return "--"
	}
	days := seconds / (60 * 60 * 24)
	hours := (seconds % (60 * 60 * 24)) / (60 * 60)
	minutes := (seconds % (60 * 60)) / 60
	secs := seconds % 60

	parts := []string{}
	if days > 0 {
		parts = append(parts, fmt.Sprintf("%d days", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d hours", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%d minutes", minutes))
	}
	if secs > 0 || len(parts) == 0 { // Always show seconds if no larger unit, or if it's 0 seconds
		parts = append(parts, fmt.Sprintf("%d seconds", secs))
	}
	return strings.Join(parts, ", ")
}

// ParseUptimeStringToSeconds 解析运行时间字符串为秒数 (迁移自原main.go)
func (su *SystemUtils) ParseUptimeStringToSeconds(uptimeStr string) int64 {
	var totalSeconds int64

	// Handle "days" part
	if strings.Contains(uptimeStr, "days") {
		parts := strings.Split(uptimeStr, "days,")
		if len(parts) == 2 {
			daysStr := strings.TrimSpace(parts[0])
			days, err := strconv.ParseInt(daysStr, 10, 64)
			if err == nil {
				totalSeconds += days * 24 * 60 * 60
			}
			uptimeStr = strings.TrimSpace(parts[1]) // Remaining part for HH:MM:SS
		}
	} else if strings.Contains(uptimeStr, "day") { // Handle "day" (singular)
		parts := strings.Split(uptimeStr, "day,")
		if len(parts) == 2 {
			daysStr := strings.TrimSpace(parts[0])
			days, err := strconv.ParseInt(daysStr, 10, 64)
			if err == nil {
				totalSeconds += days * 24 * 60 * 60
			}
			uptimeStr = strings.TrimSpace(parts[1])
		}
	}

	// Handle HH:MM:SS or MM:SS or SS part
	timeParts := strings.Split(uptimeStr, ":")
	var hours, minutes, seconds int64
	var err error

	if len(timeParts) == 3 { // HH:MM:SS
		hours, err = strconv.ParseInt(strings.TrimSpace(timeParts[0]), 10, 64)
		if err != nil {
			return 0
		}
		minutes, err = strconv.ParseInt(strings.TrimSpace(timeParts[1]), 10, 64)
		if err != nil {
			return 0
		}
		seconds, err = strconv.ParseInt(strings.TrimSpace(timeParts[2]), 10, 64)
		if err != nil {
			return 0
		}
	} else if len(timeParts) == 2 { // MM:SS (common for shorter uptimes)
		minutes, err = strconv.ParseInt(strings.TrimSpace(timeParts[0]), 10, 64)
		if err != nil {
			return 0
		}
		seconds, err = strconv.ParseInt(strings.TrimSpace(timeParts[1]), 10, 64)
		if err != nil {
			return 0
		}
	} else if len(timeParts) == 1 { // SS (very short uptimes)
		// Check if it's just seconds (e.g., "51 seconds")
		sParts := strings.Fields(uptimeStr)
		if len(sParts) == 2 && sParts[1] == "seconds" {
			seconds, err = strconv.ParseInt(strings.TrimSpace(sParts[0]), 10, 64)
			if err != nil {
				return 0
			}
		} else {
			// If it's just a number, assume it's seconds
			seconds, err = strconv.ParseInt(strings.TrimSpace(timeParts[0]), 10, 64)
			if err != nil {
				return 0
			}
		}
	} else {
		return 0 // Unknown format
	}

	totalSeconds += hours*60*60 + minutes*60 + seconds
	return totalSeconds
}

// ParseDockerUptimeToSeconds 解析Docker运行时间为秒数
func (su *SystemUtils) ParseDockerUptimeToSeconds(rawStatus string) int64 {
	// Docker状态示例: "Up 2 hours", "Up 3 days", "Up 5 minutes", "Exited (0) 2 hours ago"
	if !strings.HasPrefix(rawStatus, "Up ") {
		return 0 // 不是运行状态
	}

	// 移除 "Up " 前缀
	uptimeStr := strings.TrimPrefix(rawStatus, "Up ")
	
	var totalSeconds int64

	// 解析不同的时间单位
	if strings.Contains(uptimeStr, "day") {
		parts := strings.Fields(uptimeStr)
		for i, part := range parts {
			if strings.Contains(part, "day") && i > 0 {
				days, err := strconv.ParseInt(parts[i-1], 10, 64)
				if err == nil {
					totalSeconds += days * 24 * 60 * 60
				}
			}
		}
	}

	if strings.Contains(uptimeStr, "hour") {
		parts := strings.Fields(uptimeStr)
		for i, part := range parts {
			if strings.Contains(part, "hour") && i > 0 {
				hours, err := strconv.ParseInt(parts[i-1], 10, 64)
				if err == nil {
					totalSeconds += hours * 60 * 60
				}
			}
		}
	}

	if strings.Contains(uptimeStr, "minute") {
		parts := strings.Fields(uptimeStr)
		for i, part := range parts {
			if strings.Contains(part, "minute") && i > 0 {
				minutes, err := strconv.ParseInt(parts[i-1], 10, 64)
				if err == nil {
					totalSeconds += minutes * 60
				}
			}
		}
	}

	if strings.Contains(uptimeStr, "second") {
		parts := strings.Fields(uptimeStr)
		for i, part := range parts {
			if strings.Contains(part, "second") && i > 0 {
				seconds, err := strconv.ParseInt(parts[i-1], 10, 64)
				if err == nil {
					totalSeconds += seconds
				}
			}
		}
	}

	return totalSeconds
}

// GetProcessInfo 获取进程信息 (迁移自原main.go的进程信息获取逻辑)
func (su *SystemUtils) GetProcessInfo(pid int32) (cpuPercent float64, memoryMB string, createTime int64, err error) {
	p, err := proc.NewProcess(pid)
	if err != nil {
		return 0, "--", 0, fmt.Errorf("创建进程对象失败: %v", err)
	}

	// 获取CPU使用率
	cpuPercent, err = p.CPUPercent()
	if err != nil {
		log.Printf("获取PID %d的CPU使用率失败: %v", pid, err)
		cpuPercent = 0
	}

	// 获取内存使用情况 (RSS)
	memInfo, err := p.MemoryInfo()
	if err != nil {
		log.Printf("获取PID %d的内存信息失败: %v", pid, err)
		memoryMB = "--"
	} else {
		memoryMB = su.FormatBytes(memInfo.RSS)
	}

	// 获取进程创建时间
	createTime, err = p.CreateTime() // Unix毫秒
	if err != nil {
		log.Printf("获取PID %d的创建时间失败: %v", pid, err)
		createTime = 0
	}

	return cpuPercent, memoryMB, createTime, nil
}

// FormatBytes 格式化字节为人类可读的单位 (迁移自原main.go)
func (su *SystemUtils) FormatBytes(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}
	div, exp := uint64(unit), 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(b)/float64(div), "KMGTPE"[exp])
}

// GetSystemdServiceDetails 获取Systemd服务详细信息 (迁移自原main.go)
func (su *SystemUtils) GetSystemdServiceDetails(serviceName string) (pid string, cpu string, memory string, uptime int64, err error) {
	if runtime.GOOS == "windows" {
		return "", "--", "--", 0, fmt.Errorf("Windows系统不支持systemd")
	}

	// 获取服务详细信息
	detailOutput, err := su.ExecuteCommand("systemctl", "show", serviceName)
	if err != nil {
		return "", "--", "--", 0, fmt.Errorf("获取systemctl show失败: %v", err)
	}

	// 解析MainPID
	pidStr := su.ParseSystemdProperty(detailOutput, "MainPID")
	if pidStr == "" || pidStr == "0" {
		return "", "--", "--", 0, nil // 服务可能没有运行
	}

	pid = pidStr
	pidInt, err := strconv.ParseInt(pidStr, 10, 32)
	if err != nil || pidInt <= 0 {
		return pid, "--", "--", 0, nil
	}

	// 获取进程信息
	cpuPercent, memoryStr, createTime, err := su.GetProcessInfo(int32(pidInt))
	if err != nil {
		return pid, "--", "--", 0, nil
	}

	cpu = fmt.Sprintf("%.2f%%", cpuPercent)
	memory = memoryStr

	// 计算运行时间
	if createTime > 0 {
		uptimeSeconds := (time.Now().UnixNano() - createTime*1_000_000) / 1_000_000_000
		uptime = uptimeSeconds
	}

	return pid, cpu, memory, uptime, nil
}

// GetDockerContainerDetails 获取Docker容器详细信息
func (su *SystemUtils) GetDockerContainerDetails(containerID string) (pid string, cpu string, memory string, err error) {
	inspectOutput, err := su.ExecuteCommand("docker", "inspect", containerID)
	if err != nil {
		return "", "--", "--", fmt.Errorf("docker inspect失败: %v", err)
	}

	var inspectData []struct {
		State struct {
			Pid int `json:"Pid"`
		} `json:"State"`
	}

	err = json.Unmarshal([]byte(inspectOutput), &inspectData)
	if err != nil || len(inspectData) == 0 {
		return "", "--", "--", fmt.Errorf("解析docker inspect输出失败: %v", err)
	}

	pidInt := inspectData[0].State.Pid
	if pidInt <= 0 {
		return "", "--", "--", nil // 容器可能没有运行
	}

	pid = fmt.Sprintf("%d", pidInt)

	// 获取进程信息
	cpuPercent, memoryStr, _, err := su.GetProcessInfo(int32(pidInt))
	if err != nil {
		return pid, "--", "--", nil
	}

	cpu = fmt.Sprintf("%.2f%%", cpuPercent)
	memory = memoryStr

	return pid, cpu, memory, nil
}

// IsCommandAvailable 检查命令是否可用
func (su *SystemUtils) IsCommandAvailable(command string) bool {
	_, err := exec.LookPath(command)
	return err == nil
}

// GetOSInfo 获取操作系统信息
func (su *SystemUtils) GetOSInfo() (string, string, string) {
	return runtime.GOOS, runtime.GOARCH, runtime.Version()
}

// ValidateServiceName 验证服务名称
func (su *SystemUtils) ValidateServiceName(serviceName string) error {
	if serviceName == "" {
		return fmt.Errorf("服务名称不能为空")
	}

	// 检查是否包含危险字符
	dangerousChars := []string{";", "&", "|", "`", "$", "(", ")", "{", "}", "[", "]"}
	for _, char := range dangerousChars {
		if strings.Contains(serviceName, char) {
			return fmt.Errorf("服务名称包含危险字符: %s", char)
		}
	}

	return nil
}

// SanitizeOutput 清理命令输出
func (su *SystemUtils) SanitizeOutput(output string) string {
	// 移除ANSI转义序列
	output = strings.ReplaceAll(output, "\x1b[", "")
	
	// 限制输出长度
	if len(output) > 10000 {
		output = output[:10000] + "\n... (输出被截断)"
	}

	return output
}
