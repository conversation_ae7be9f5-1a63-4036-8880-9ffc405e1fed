package handlers

import (
	fiber "github.com/gofiber/fiber/v2"         // 导入 Fiber 框架
	websocket "github.com/gofiber/websocket/v2" // 导入 WebSocket 库
)

// SetupRoutes 设置所有路由
func SetupRoutes(app *fiber.App) {
	// 设置 API 路由
	setupAPIRoutes(app)

	// 设置 WebSocket 路由
	setupWebSocketRoutes(app)
}

// setupAPIRoutes 设置 API 路由
func setupAPIRoutes(app *fiber.App) {
	// API 路由组
	api := app.Group("/api")

	// 健康检查路由
	api.Get("/health", HealthCheckHandler)

	// 认证相关路由
	auth := api.Group("/auth")
	auth.Post("/login", LoginHandler)
	auth.Post("/logout", LogoutHandler)

	// 服务器监控相关路由
	servers := api.Group("/servers")
	servers.Get("/", GetServersHandler)
	servers.Get("/:id", GetServerByIDHandler)

	// 系统统计相关路由
	system := app.Group("/system") // 修正：应为 app.Group 而非 api.Group
	system.Get("/stats", GetSystemStatsHandler)

	// 服务管理相关路由
	services := api.Group("/services")
	services.Get("/list", GetServicesListHandler)
	services.Post("/:type/:action", ServiceActionHandler)
	services.Get("/logs", GetServiceLogsHandler)
}

// setupWebSocketRoutes 设置 WebSocket 路由
func setupWebSocketRoutes(app *fiber.App) {
	// 启动 WebSocket 管理器
	StartWebSocketManager()

	// 客户端 WebSocket 连接 (用于监控数据上报)
	app.Get("/ws", websocket.New(HandleClientWebSocket))

	// 前端 WebSocket 连接 (用于实时数据推送)
	app.Get("/ws/frontend", websocket.New(HandleFrontendWebSocket))
}

// HandleClientWebSocket 处理客户端 WebSocket 连接
func HandleClientWebSocket(c *websocket.Conn) {
	// 这里将调用 websocket 包中的实际处理器
	// wsmanager.HandleClientWebSocket(c)
}

// HandleFrontendWebSocket 处理前端 WebSocket 连接
func HandleFrontendWebSocket(c *websocket.Conn) {
	// 这里将调用 websocket 包中的实际处理器
	// wsmanager.HandleFrontendWebSocket(c)
}

// StartWebSocketManager 启动 WebSocket 管理器
func StartWebSocketManager() {
	// 这里将调用 websocket 包中的启动函数
	// wsmanager.StartWebSocketManager()
}
