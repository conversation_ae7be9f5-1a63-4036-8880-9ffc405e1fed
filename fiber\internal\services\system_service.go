package services

import (
	"fmt"
	"log"
	"time"

	"github.com/shirou/gopsutil/v4/cpu"
	"github.com/shirou/gopsutil/v4/disk"
	"github.com/shirou/gopsutil/v4/host"
	"github.com/shirou/gopsutil/v4/load"
	"github.com/shirou/gopsutil/v4/mem"
	"github.com/shirou/gopsutil/v4/net"

	"server-monitor-fiber/internal/database"
)

// SystemService 系统服务层 (迁移自原main.go 600-1000行的系统统计功能)
type SystemService struct {
	networkTracker *NetworkSpeedTracker
}

// NetworkSpeedTracker 网络速度跟踪器
type NetworkSpeedTracker struct {
	LastNetIn  uint64
	LastNetOut uint64
	LastTime   time.Time
}

// NewSystemService 创建系统服务实例
func NewSystemService() *SystemService {
	return &SystemService{
		networkTracker: &NetworkSpeedTracker{
			LastTime: time.Now(),
		},
	}
}

// GetCurrentSystemStats 获取当前系统统计信息 (迁移自原main.go getCurrentSystemStats)
func (s *SystemService) GetCurrentSystemStats() (*database.StatusInfo, error) {
	// 获取CPU使用率
	cpuPercent, err := cpu.Percent(time.Second, false)
	var cpuUsage float64
	if err == nil && len(cpuPercent) > 0 {
		cpuUsage = cpuPercent[0]
	}

	// 获取内存信息
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		log.Printf("获取内存信息错误: %v", err)
		return nil, fmt.Errorf("获取内存信息失败: %v", err)
	}
	log.Printf("调试: getCurrentSystemStats - MemTotal: %d", memInfo.Total)

	// 获取磁盘信息
	diskInfo, err := disk.Usage("/")
	if err != nil {
		diskInfo, err = disk.Usage("C:")
		if err != nil {
			return nil, fmt.Errorf("获取磁盘信息失败: %v", err)
		}
	}

	// 获取平均负载
	loadInfo, err := load.Avg()
	var load1, load5, load15 float64
	if err == nil {
		load1 = loadInfo.Load1
		load5 = loadInfo.Load5
		load15 = loadInfo.Load15
	}

	// 获取网络I/O
	netInfo, err := net.IOCounters(false)
	var netIn, netOut uint64
	if err == nil && len(netInfo) > 0 {
		netIn = netInfo[0].BytesRecv
		netOut = netInfo[0].BytesSent
	}

	// 使用网络跟踪器计算网络速度
	netInSpeed, netOutSpeed := s.networkTracker.calculateNetworkSpeed(netIn, netOut)

	// 获取交换信息
	swapInfo, _ := mem.SwapMemory()
	var swapUsed uint64
	if swapInfo != nil {
		swapUsed = swapInfo.Used
	}

	// 获取主机正常运行时间信息
	hostInfo, err := host.Info()
	var uptime uint64
	var processCount int
	if err == nil {
		uptime = uint64(time.Now().Unix() - int64(hostInfo.BootTime))
		processCount = int(hostInfo.Procs)
	}

	// 创建状态信息
	stats := &database.StatusInfo{
		CPU:         cpuUsage,
		MemUsed:     memInfo.Used,
		MemTotal:    memInfo.Total,
		DiskUsed:    diskInfo.Used,
		DiskTotal:   diskInfo.Total,
		NetInSpeed:  netInSpeed,  // 实时下载速度
		NetOutSpeed: netOutSpeed, // 实时上传速度
		Uptime:      uptime,
		Load1:       load1,
		Load5:       load5,
		Load15:      load15,
	}

	return stats, nil
}

// calculateNetworkSpeed 计算网络速度 (迁移自原main.go)
func (nt *NetworkSpeedTracker) calculateNetworkSpeed(currentNetIn, currentNetOut uint64) (uint64, uint64) {
	now := time.Now()
	timeDiff := now.Sub(nt.LastTime).Seconds()

	var inSpeed, outSpeed uint64

	// 仅在我们有先前数据和合理时间差的情况下计算速度
	isFirstRun := nt.LastNetIn == 0 && nt.LastNetOut == 0
	if timeDiff > 0 && timeDiff < 300 && !isFirstRun {
		// 计算每秒字节数
		if currentNetIn >= nt.LastNetIn {
			inSpeed = uint64(float64(currentNetIn-nt.LastNetIn) / timeDiff)
		}
		if currentNetOut >= nt.LastNetOut {
			outSpeed = uint64(float64(currentNetOut-nt.LastNetOut) / timeDiff)
		}
	}

	// 更新跟踪数据
	nt.LastNetIn = currentNetIn
	nt.LastNetOut = currentNetOut
	nt.LastTime = now

	return inSpeed, outSpeed
}

// GetLatestClientStats 获取最新的客户端统计信息
func (s *SystemService) GetLatestClientStats(serverID int) (*database.StatusInfo, bool) {
	// 这里应该从缓存或数据库获取最新的客户端统计信息
	// 暂时返回模拟数据
	stats := &database.StatusInfo{
		CPU:         45.2,
		MemUsed:     8589934592,  // 8GB
		MemTotal:    17179869184, // 16GB
		DiskUsed:    536870912000,  // 500GB
		DiskTotal:   1073741824000, // 1TB
		NetInSpeed:  1048576,  // 1MB/s
		NetOutSpeed: 524288,   // 512KB/s
		Uptime:      1296000,  // 15 days
		Load1:       0.5,
		Load5:       0.3,
		Load15:      0.2,
	}

	return stats, true
}

// FormatSystemStats 格式化系统统计信息用于API响应
func (s *SystemService) FormatSystemStats(stats *database.StatusInfo) map[string]interface{} {
	return map[string]interface{}{
		"CPU":            stats.CPU,
		"MemUsed":        stats.MemUsed,
		"MemTotal":       stats.MemTotal,
		"memory":         s.calculatePercentage(stats.MemUsed, stats.MemTotal),
		"DiskUsed":       stats.DiskUsed,
		"DiskTotal":      stats.DiskTotal,
		"disk":           s.calculatePercentage(stats.DiskUsed, stats.DiskTotal),
		"NetInSpeed":     stats.NetInSpeed,
		"NetOutSpeed":    stats.NetOutSpeed,
		"NetInTransfer":  0, // 可以添加网络传输总量
		"NetOutTransfer": 0,
		"Uptime":         stats.Uptime,
		"LoadAvg":        []float64{stats.Load1, stats.Load5, stats.Load15},
		"Processes":      0, // 可以添加进程数量
		"timestamp":      time.Now(),
	}
}

// calculatePercentage 计算百分比
func (s *SystemService) calculatePercentage(used, total uint64) float64 {
	if total == 0 {
		return 0
	}
	return (float64(used) / float64(total)) * 100
}

// GetSystemHealth 获取系统健康状态
func (s *SystemService) GetSystemHealth() map[string]interface{} {
	stats, err := s.GetCurrentSystemStats()
	if err != nil {
		return map[string]interface{}{
			"status":  "error",
			"message": "无法获取系统统计信息",
			"error":   err.Error(),
		}
	}

	// 计算健康状态
	status := "healthy"
	var warnings []string

	// 检查CPU使用率
	if stats.CPU > 90 {
		status = "critical"
		warnings = append(warnings, "CPU使用率过高")
	} else if stats.CPU > 80 {
		status = "warning"
		warnings = append(warnings, "CPU使用率较高")
	}

	// 检查内存使用率
	memoryPercent := s.calculatePercentage(stats.MemUsed, stats.MemTotal)
	if memoryPercent > 95 {
		status = "critical"
		warnings = append(warnings, "内存使用率过高")
	} else if memoryPercent > 85 {
		if status != "critical" {
			status = "warning"
		}
		warnings = append(warnings, "内存使用率较高")
	}

	// 检查磁盘使用率
	diskPercent := s.calculatePercentage(stats.DiskUsed, stats.DiskTotal)
	if diskPercent > 95 {
		status = "critical"
		warnings = append(warnings, "磁盘空间不足")
	} else if diskPercent > 85 {
		if status != "critical" {
			status = "warning"
		}
		warnings = append(warnings, "磁盘空间较少")
	}

	// 检查负载平均值
	if stats.Load1 > 10 {
		status = "critical"
		warnings = append(warnings, "系统负载过高")
	} else if stats.Load1 > 5 {
		if status != "critical" {
			status = "warning"
		}
		warnings = append(warnings, "系统负载较高")
	}

	return map[string]interface{}{
		"status":    status,
		"warnings":  warnings,
		"stats":     s.FormatSystemStats(stats),
		"timestamp": time.Now(),
	}
}

// GetNetworkStats 获取网络统计信息
func (s *SystemService) GetNetworkStats() (map[string]interface{}, error) {
	netInfo, err := net.IOCounters(false)
	if err != nil {
		return nil, fmt.Errorf("获取网络统计信息失败: %v", err)
	}

	if len(netInfo) == 0 {
		return nil, fmt.Errorf("没有可用的网络接口")
	}

	// 计算网络速度
	netInSpeed, netOutSpeed := s.networkTracker.calculateNetworkSpeed(
		netInfo[0].BytesRecv,
		netInfo[0].BytesSent,
	)

	return map[string]interface{}{
		"bytes_recv":    netInfo[0].BytesRecv,
		"bytes_sent":    netInfo[0].BytesSent,
		"packets_recv":  netInfo[0].PacketsRecv,
		"packets_sent":  netInfo[0].PacketsSent,
		"errin":         netInfo[0].Errin,
		"errout":        netInfo[0].Errout,
		"dropin":        netInfo[0].Dropin,
		"dropout":       netInfo[0].Dropout,
		"speed_in":      netInSpeed,
		"speed_out":     netOutSpeed,
		"timestamp":     time.Now(),
	}, nil
}

// GetDiskStats 获取磁盘统计信息
func (s *SystemService) GetDiskStats() ([]map[string]interface{}, error) {
	partitions, err := disk.Partitions(false)
	if err != nil {
		return nil, fmt.Errorf("获取磁盘分区信息失败: %v", err)
	}

	var diskStats []map[string]interface{}

	for _, partition := range partitions {
		usage, err := disk.Usage(partition.Mountpoint)
		if err != nil {
			log.Printf("获取分区 %s 使用情况失败: %v", partition.Mountpoint, err)
			continue
		}

		diskStats = append(diskStats, map[string]interface{}{
			"device":      partition.Device,
			"mountpoint":  partition.Mountpoint,
			"fstype":      partition.Fstype,
			"total":       usage.Total,
			"used":        usage.Used,
			"free":        usage.Free,
			"used_percent": usage.UsedPercent,
		})
	}

	return diskStats, nil
}
