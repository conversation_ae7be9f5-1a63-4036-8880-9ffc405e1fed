package services

import (
	"encoding/json"
	"fmt"
	"log"
	"time"

	"server-monitor-fiber/internal/database"
	"server-monitor-fiber/internal/websocket"
)

// ServerService 服务器服务层 (迁移自原main.go 300-600行的业务逻辑)
type ServerService struct {
	serverStatusRepo database.ServerStatusRepository
	serverInfoRepo   database.ServerInfoRepository
	serviceRepo      database.ServiceRepository
}

// NewServerService 创建服务器服务实例
func NewServerService(
	statusRepo database.ServerStatusRepository,
	infoRepo database.ServerInfoRepository,
	svcRepo database.ServiceRepository,
) *ServerService {
	return &ServerService{
		serverStatusRepo: statusRepo,
		serverInfoRepo:   infoRepo,
		serviceRepo:      svcRepo,
	}
}

// ProcessServerData 处理服务器数据 (迁移自原main.go的核心处理逻辑)
func (s *ServerService) ProcessServerData(serverDetails *database.ServerDetails, msg *database.Message) error {
	log.Printf("处理服务器 %d 的数据", serverDetails.ID)

	// 1. 保存服务器状态
	serverStatus := serverDetails.ToServerStatus()
	if err := s.serverStatusRepo.SaveServerStatus(serverStatus); err != nil {
		log.Printf("保存服务器状态失败: %v", err)
		return fmt.Errorf("保存服务器状态失败: %w", err)
	}

	// 2. 更新服务器信息
	serverInfo := serverDetails.ToServerInfo()
	if err := s.serverInfoRepo.UpdateServerInfo(serverInfo); err != nil {
		log.Printf("更新服务器信息失败: %v", err)
		// 不返回错误，继续处理
	}

	// 3. 保存历史数据
	history := serverDetails.ToServerStatusHistory()
	if err := s.serverStatusRepo.CreateServerStatusHistory(history); err != nil {
		log.Printf("保存历史数据失败: %v", err)
		// 不返回错误，继续处理
	}

	// 4. 异步处理服务数据
	go s.processServiceData(serverDetails.ID, msg)

	// 5. 广播到前端
	s.broadcastServerData(serverDetails.ID, serverStatus.ToStatusInfo())

	log.Printf("服务器 %d 数据处理完成", serverDetails.ID)
	return nil
}

// processServiceData 处理服务数据 (异步)
func (s *ServerService) processServiceData(serverID int, msg *database.Message) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("处理服务数据时发生panic: %v", r)
		}
	}()

	// 处理服务更新
	s.serviceRepo.ProcessServiceUpdates(
		serverID,
		msg.SupervisorServices,
		msg.SystemdServices,
		msg.DockerServices,
	)

	log.Printf("服务器 %d 的服务数据处理完成", serverID)
}

// broadcastServerData 广播服务器数据到前端
func (s *ServerService) broadcastServerData(serverID int, statusInfo *database.StatusInfo) {
	// 创建系统统计数据
	systemStats := websocket.SystemStats{
		CPU:            statusInfo.CPU,
		MemUsed:        statusInfo.MemUsed,
		MemTotal:       statusInfo.MemTotal,
		Memory:         s.calculatePercentage(statusInfo.MemUsed, statusInfo.MemTotal),
		DiskUsed:       statusInfo.DiskUsed,
		DiskTotal:      statusInfo.DiskTotal,
		Disk:           s.calculatePercentage(statusInfo.DiskUsed, statusInfo.DiskTotal),
		NetInSpeed:     statusInfo.NetInSpeed,
		NetOutSpeed:    statusInfo.NetOutSpeed,
		NetInTransfer:  0, // 这里可以添加网络传输总量
		NetOutTransfer: 0,
		Uptime:         statusInfo.Uptime,
		LoadAvg:        []float64{statusInfo.Load1, statusInfo.Load5, statusInfo.Load15},
		Processes:      0, // 这里可以添加进程数量
	}

	// 创建广播消息
	broadcastMsg := websocket.WSMessage{
		Type:     websocket.WSMsgTypeSystemStatsBroadcast,
		ServerID: serverID,
		Data: websocket.SystemStatsBroadcastMessage{
			ServerID: serverID,
			Data:     systemStats,
		},
		Timestamp: time.Now(),
	}

	// 广播到前端
	websocket.GlobalManager.BroadcastToFrontends(serverID, broadcastMsg)
}

// calculatePercentage 计算百分比
func (s *ServerService) calculatePercentage(used, total uint64) float64 {
	if total == 0 {
		return 0
	}
	return (float64(used) / float64(total)) * 100
}

// GetServerStatus 获取服务器状态
func (s *ServerService) GetServerStatus(serverID int) (*database.ServerStatus, error) {
	return s.serverStatusRepo.GetServerStatus(serverID)
}

// GetServerInfo 获取服务器信息
func (s *ServerService) GetServerInfo(serverID int) (*database.ServerInfo, error) {
	return s.serverInfoRepo.GetServerInfo(serverID)
}

// GetAllServerStatuses 获取所有服务器状态
func (s *ServerService) GetAllServerStatuses(serverIDs []int) ([]database.ServerStatus, error) {
	return s.serverStatusRepo.GetAllServerStatuses(serverIDs)
}

// GetServerStatusHistory 获取服务器状态历史
func (s *ServerService) GetServerStatusHistory(serverID int, limit int) ([]database.ServerStatusHistory, error) {
	return s.serverStatusRepo.GetServerStatusHistory(serverID, limit)
}

// GetSupervisorServices 获取Supervisor服务
func (s *ServerService) GetSupervisorServices(serverID int) ([]database.SupervisorService, error) {
	return s.serviceRepo.GetSupervisorServices(serverID)
}

// GetSystemdServices 获取Systemd服务
func (s *ServerService) GetSystemdServices(serverID int) ([]database.SystemdService, error) {
	return s.serviceRepo.GetSystemdServices(serverID)
}

// GetDockerServices 获取Docker服务
func (s *ServerService) GetDockerServices(serverID int) ([]database.DockerService, error) {
	return s.serviceRepo.GetDockerServices(serverID)
}

// ValidateServerData 验证服务器数据 (迁移自原main.go的验证逻辑)
func (s *ServerService) ValidateServerData(serverDetails *database.ServerDetails) error {
	if serverDetails.ID <= 0 {
		return fmt.Errorf("无效的服务器ID: %d", serverDetails.ID)
	}

	if serverDetails.Name == "" {
		return fmt.Errorf("服务器名称不能为空")
	}

	// 验证状态数据的合理性
	if serverDetails.Status.CPU < 0 || serverDetails.Status.CPU > 100 {
		log.Printf("警告: 服务器 %d 的CPU使用率异常: %.2f%%", serverDetails.ID, serverDetails.Status.CPU)
	}

	if serverDetails.Status.MemTotal > 0 && serverDetails.Status.MemUsed > serverDetails.Status.MemTotal {
		log.Printf("警告: 服务器 %d 的内存使用量超过总量", serverDetails.ID)
	}

	return nil
}

// FormatServerData 格式化服务器数据用于前端显示
func (s *ServerService) FormatServerData(serverStatus *database.ServerStatus) map[string]interface{} {
	return map[string]interface{}{
		"id":             serverStatus.ID,
		"name":           serverStatus.Name,
		"tag":            serverStatus.Tag,
		"last_active":    serverStatus.LastActive,
		"ipv4":           serverStatus.IPv4,
		"ipv6":           serverStatus.IPv6,
		"valid_ip":       serverStatus.ValidIP,
		"cpu":            serverStatus.StatusCPU,
		"memory":         s.calculatePercentage(serverStatus.StatusMemUsed, serverStatus.StatusMemTotal),
		"mem_used":       serverStatus.StatusMemUsed,
		"mem_total":      serverStatus.StatusMemTotal,
		"disk":           s.calculatePercentage(serverStatus.StatusDiskUsed, serverStatus.StatusDiskTotal),
		"disk_used":      serverStatus.StatusDiskUsed,
		"disk_total":     serverStatus.StatusDiskTotal,
		"net_in_speed":   serverStatus.StatusNetInSpeed,
		"net_out_speed":  serverStatus.StatusNetOutSpeed,
		"uptime":         serverStatus.StatusUptime,
		"load1":          serverStatus.StatusLoad1,
		"load5":          serverStatus.StatusLoad5,
		"load15":         serverStatus.StatusLoad15,
		"status":         s.calculateServerStatus(serverStatus),
		"updated_at":     serverStatus.UpdatedAt,
	}
}

// calculateServerStatus 计算服务器状态
func (s *ServerService) calculateServerStatus(serverStatus *database.ServerStatus) string {
	now := time.Now().Unix()
	timeDiff := now - serverStatus.LastActive

	// 检查是否离线 (超过60秒没有数据)
	if timeDiff > 60 {
		return "offline"
	}

	// 检查是否为警告状态
	memoryPercent := s.calculatePercentage(serverStatus.StatusMemUsed, serverStatus.StatusMemTotal)
	if serverStatus.StatusCPU > 85 || memoryPercent > 90 {
		return "warning"
	}

	// 检查是否为关键状态
	if serverStatus.StatusCPU > 95 || memoryPercent > 95 {
		return "critical"
	}

	return "online"
}

// GetServerSummary 获取服务器摘要信息
func (s *ServerService) GetServerSummary() (map[string]interface{}, error) {
	// 这里可以实现获取所有服务器的摘要信息
	// 包括在线数量、离线数量、警告数量等
	
	summary := map[string]interface{}{
		"total_servers":   12, // 固定12个服务器位置
		"online_servers":  0,
		"offline_servers": 12,
		"warning_servers": 0,
		"last_updated":    time.Now(),
	}

	return summary, nil
}

// CleanupOldData 清理旧数据 (定期任务)
func (s *ServerService) CleanupOldData(retentionDays int) error {
	log.Printf("开始清理 %d 天前的历史数据", retentionDays)
	
	// 这里可以实现清理逻辑
	// 删除超过保留期的历史数据
	
	log.Printf("历史数据清理完成")
	return nil
}
